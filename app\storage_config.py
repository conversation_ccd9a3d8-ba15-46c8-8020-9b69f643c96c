import os
import logging
from typing import Optional
from llama_index.core.storage.storage_context import StorageContext
from llama_index.core.storage.docstore import SimpleDocumentStore
from llama_index.core.storage.index_store import SimpleIndexStore
from llama_index.vector_stores.chroma import ChromaVectorStore
import chromadb
from chromadb.config import Settings as ChromaSettings

logger = logging.getLogger(__name__)

def get_storage_context(storage_dir: str = "storage") -> StorageContext:
    """
    Create a storage context using SQLite for docstore/index store and ChromaDB for vector store.
    
    Args:
        storage_dir: Directory to store the databases
        
    Returns:
        StorageContext configured with SQLite and ChromaDB backends
    """
    # Ensure storage directory exists
    os.makedirs(storage_dir, exist_ok=True)
    
    # Configure ChromaDB client
    chroma_db_path = os.path.join(storage_dir, "chroma_db")
    chroma_client = chromadb.PersistentClient(
        path=chroma_db_path,
        settings=ChromaSettings(
            anonymized_telemetry=False,
            allow_reset=True
        )
    )
    
    # Get or create collection for vector storage
    collection_name = "document_vectors"
    try:
        chroma_collection = chroma_client.get_collection(collection_name)
        logger.info(f"Using existing ChromaDB collection: {collection_name}")
    except Exception:
        chroma_collection = chroma_client.create_collection(collection_name)
        logger.info(f"Created new ChromaDB collection: {collection_name}")
    
    # Create ChromaDB vector store
    vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
    
    # Configure document store (will persist to JSON for now, but in storage_dir)
    docstore = SimpleDocumentStore()

    # Configure index store (will persist to JSON for now, but in storage_dir)
    index_store = SimpleIndexStore()
    
    # Create storage context
    storage_context = StorageContext.from_defaults(
        vector_store=vector_store,
        docstore=docstore,
        index_store=index_store
    )
    
    logger.info(f"Storage context created with:")
    logger.info(f"  - ChromaDB vector store: {chroma_db_path}")
    logger.info(f"  - Document store: JSON-based in {storage_dir}")
    logger.info(f"  - Index store: JSON-based in {storage_dir}")
    
    return storage_context


def load_storage_context(storage_dir: str = "storage") -> Optional[StorageContext]:
    """
    Load existing storage context from ChromaDB and JSON stores.

    Args:
        storage_dir: Directory containing the databases

    Returns:
        StorageContext if databases exist, None otherwise
    """
    # Check if storage directory exists
    if not os.path.exists(storage_dir):
        logger.info(f"Storage directory {storage_dir} does not exist")
        return None

    # Check if ChromaDB directory exists
    chroma_db_path = os.path.join(storage_dir, "chroma_db")
    if not os.path.exists(chroma_db_path):
        logger.info(f"ChromaDB directory {chroma_db_path} does not exist")
        return None

    try:
        # Configure ChromaDB client
        chroma_client = chromadb.PersistentClient(
            path=chroma_db_path,
            settings=ChromaSettings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )

        # Get existing collection
        collection_name = "document_vectors"
        chroma_collection = chroma_client.get_collection(collection_name)
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)

        # Create storage context and load from existing files
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store,
            persist_dir=storage_dir
        )

        logger.info(f"Loaded existing storage context from {storage_dir}")
        return storage_context

    except Exception as e:
        logger.error(f"Failed to load storage context: {e}")
        return None
